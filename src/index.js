// A handler class designed to capture the raw HTML of elements it's attached to.
class HtmlBuilder {
	constructor() {
		this.content = '';
	}
	// Called for every element encountered
	element(el) {
		// Reconstruct the opening tag from its name and attributes
		const attrs = Array.from(el.attributes)
			.map(([name, value]) => `${name}="${value.replace(/"/g, '&quot;')}"`)
			.join(' ');
		
		this.content += `<${el.tagName}${attrs ? ' ' + attrs : ''}>`;

		// Void elements do not have closing tags, so we shouldn't set a handler for them.
		// List from: https://developer.mozilla.org/en-US/docs/Glossary/Void_element
		const voidElements = new Set([
			'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 
			'input', 'link', 'meta', 'param', 'source', 'track', 'wbr'
		]);

		if (voidElements.has(el.tagName.toLowerCase())) {
			return;
		}

		// Set a hook to append the closing tag when it's found
		el.onEndTag(end => {
			// Use end.name, which is the tag name of the end tag.
			this.content += `</${end.name}>`;
		});
	}
	// Called for any text content between elements
	text(text) {
		if (text && text.text) {
			this.content += text.text;
		}
	}
	// Called for any comments
	comments(comment) {
		// The `text` property of a comment does not include the `<!--` and `-->`.
		// We need to add them back to reconstruct the comment node.
		if (comment) {
			this.content += `<!--${comment.text}-->`;
		}
	}
}

export default {
	async fetch(request) {
		// 1. Get reference from the URL (e.g., /NIV/John/1)
		const url = new URL(request.url);
		const pathParts = url.pathname.split('/').filter(p => p);
		if (pathParts.length < 3) {
			return new Response('Usage: /{Translation}/{Book}/{Chapter}, for example /NIV/John/1', { status: 400 });
		}
		const [translation, book, chapter] = pathParts;
		const passageRef = `${book} ${chapter}`;

		// 2. Construct and fetch the Bible Gateway URL
		const passageUrl = `https://www.biblegateway.com/passage/?search=${passageRef}&version=${translation}`;
		const response = await fetch(passageUrl, {
			headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36' },
		});

		// Check if the response is valid
		if (!response.ok) {
			return new Response(`Failed to fetch passage from Bible Gateway: ${response.status} ${response.statusText}`, { status: 500 });
		}

		// 3. Set up variables and handlers for extraction
		let title = `${passageRef} - ${translation}`; // Default title
		const passageHandler = new HtmlBuilder();

		const rewriter = new HTMLRewriter()
			// This selector is key: it targets every direct child element of '.std-text'
			.on('div.std-text > *', passageHandler)
			// This handler grabs the text content of the title element
			.on('.passage-display .bcv', { // Corrected selector
				text(text) {
					// Use `if (text.text)` to build title, preventing overwrites by empty text nodes
					if (text.text.trim()) {
						title = text.text.trim();
					}
				},
			})
			.on('sup.crossreference', {
				element: el => el.remove(),
			})
			.on('sup.footnote', {
				element: el => el.remove(),
			})
			.on('sup.versenum', {
				element: el => el.remove(),
			})
			.on('.std-text h3', {
				element: el => el.remove(),
			});

		// 4. Transform the response stream. This populates our variables.
		// We must read the entire body for the handlers to complete.
		try {
			const transformedResponse = rewriter.transform(response);
			await transformedResponse.arrayBuffer();
		} catch (error) {
			console.error('Error transforming response:', error);
			return new Response(`Error processing Bible Gateway response: ${error.message}`, { status: 500 });
		}

		const passageHtml = passageHandler.content;

		// Check if we successfully extracted content
		if (!passageHtml) {
			return new Response(`Could not find passage content for "${passageRef}" in translation "${translation}". The website's HTML structure might have changed.`, {
				status: 404,
			});
		}

		// 5. Rebuild the final, minimal HTML page with the extracted content
		const finalHtml = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title.trim()}</title>
        <style>
          body { 
            font-family: "Bookerly", "Caecilia", Georgia, serif; 
            font-size: 1.3em;
            line-height: 1.6; 
            max-width: 700px; 
            margin: 1em auto; 
            padding: 0 1em;
            background-color: #f7f7f7;
            color: #111;
          }
          h1 { 
            text-align: center; 
            font-family: sans-serif;
            font-weight: normal;
            font-size: 1.5em;
            margin-bottom: 2em;
          }
          h3 {
             font-size: 1em;
             font-style: italic;
             text-align: center;
             color: #555;
          }
          p { margin-bottom: 1em; }
          /* This will hide verse numbers and footnotes for now. */
          /* We will improve this removal in the next step. */
          footer { margin-top: 4em; text-align: center; font-size: 0.8em; color: #777;}
        </style>
      </head>
      <body>
        <h1>${title.trim()}</h1>
        ${passageHtml}
        <footer>
          <p>Passage text retrieved from BibleGateway.com</p>
          <p>Quoted text from ${translation.toUpperCase()}&reg;</p>
        </footer>
      </body>
      </html>
    `;

		return new Response(finalHtml, {
			headers: { 'Content-Type': 'text/html' },
		});
	},
};